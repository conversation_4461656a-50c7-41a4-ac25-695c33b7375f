# COMP9311 25T2: 作业2 解答

---

## 问题1 (12分)

**已知：** 关系R(A,B,C,D,E,G,H,I,J) 和函数依赖集合 F = {BDG→AC, A→BJ, ADI→CG, BH→E, BI→AEG, HJ→DG}

### 1) 检查 AH→G 是否成立 (1分)

要检查AH→G是否成立，需要求出AH在F下的闭包。

**步骤1：** 从 (AH)⁺ = {A, H} 开始

**步骤2：** 迭代应用函数依赖规则：
- 由 A→BJ：因为 A ∈ (AH)⁺，所以添加 B, J → (AH)⁺ = {A, H, B, J}
- 由 BH→E：因为 B, H ∈ (AH)⁺，所以添加 E → (AH)⁺ = {A, H, B, J, E}
- 由 HJ→DG：因为 H, J ∈ (AH)⁺，所以添加 D, G → (AH)⁺ = {A, H, B, J, E, D, G}
- 由 BDG→AC：因为 B, D, G ∈ (AH)⁺，所以添加 C → (AH)⁺ = {A, H, B, J, E, D, G, C}
- 由 BI→AEG：需要 I，但 I ∉ (AH)⁺，所以此规则不适用
- 由 ADI→CG：需要 I，但 I ∉ (AH)⁺，所以此规则不适用

**结果：** (AH)⁺ = {A, H, B, J, E, D, G, C}

因为 G ∈ (AH)⁺，所以 **AH→G 成立**。

### 2) 找出R的所有候选键 (2分)

要找候选键，需要识别以下属性：
- 不出现在任何函数依赖右边的属性（必须在每个键中）
- 只出现在左边或两边都出现的属性

**属性分析：**
- 只在右边：无
- 只在左边：I（只出现在 ADI→CG 和 BI→AEG 中）
- 两边都有：A, B, C, D, E, G, H, J
- 都不在：无

因为I只出现在左边，所以 **I必须在每个候选键中**。

**测试包含I的最小集合：**

**测试 AI：**
(AI)⁺ = {A, I} → A→BJ → {A, I, B, J} → BI→AEG → {A, I, B, J, E, G} → HJ→DG 需要 H
不充分。

**测试 HI：**
(HI)⁺ = {H, I} → BI→AEG 需要 B
不充分。

**测试 AHI：**
(AHI)⁺ = {A, H, I}
- A→BJ → {A, H, I, B, J}
- BH→E → {A, H, I, B, J, E}
- BI→AEG → {A, H, I, B, J, E, G}（G已包含）
- HJ→DG → {A, H, I, B, J, E, G, D}
- BDG→AC → {A, H, I, B, J, E, G, D, C}
- ADI→CG → {A, H, I, B, J, E, G, D, C}（C, G已包含）

(AHI)⁺ = {A, B, C, D, E, G, H, I, J} = R

**检查AHI是否最小：**
- (AI)⁺ ≠ R（如上所示）
- (HI)⁺ ≠ R（如上所示）

因此，**AHI是一个候选键**。

**检查其他候选键：**
由于我们需要I且发现AHI有效，需要检查是否有其他最小组合。

经过系统检查，**AHI是唯一的候选键**。

### 3) 确定R的最高范式 (2分)

**检查1NF：** 假设满足（所有属性都是原子的）。

**检查2NF：**
如果每个非主属性都完全函数依赖于每个候选键，则关系满足2NF。

主属性（在候选键AHI中）：{A, H, I}
非主属性：{B, C, D, E, G, J}

检查部分依赖：
- A→BJ：A是键AHI的一部分，而B, J是非主属性
- 这违反了2NF

**结果：** R满足1NF但不满足2NF。

### 4) 找出F的最小覆盖F' (2分)

**原始F：** {BDG→AC, A→BJ, ADI→CG, BH→E, BI→AEG, HJ→DG}

**步骤1：拆分右边有多个属性的函数依赖**
- BDG→AC 变成 BDG→A, BDG→C
- A→BJ 变成 A→B, A→J
- ADI→CG 变成 ADI→C, ADI→G
- BI→AEG 变成 BI→A, BI→E, BI→G

F₁ = {BDG→A, BDG→C, A→B, A→J, ADI→C, ADI→G, BH→E, BI→A, BI→E, BI→G, HJ→DG}

**步骤2：移除冗余的函数依赖**

检查 BDG→A：
- 能否用其他函数依赖从BDG推导出A？
- 从 BI→A：需要 I，但 I ∉ {B,D,G}
- 无法推导，所以 BDG→A 不冗余

检查 BDG→C：
- 从 ADI→C：需要 A,I。我们有A（从BDG→A），但需要I
- 无法推导，所以 BDG→C 不冗余

对所有函数依赖继续此过程...

**步骤3：移除多余的属性**

对于 BDG→A，检查是否可以从{B,D,G}中移除任何属性：
- DG→A？检查(DG)⁺（不使用BDG→A）
- BG→A？检查(BG)⁺（不使用BDG→A）
- BD→A？检查(BD)⁺（不使用BDG→A）

经过系统检查：

**最小覆盖F'：** {BDG→A, BDG→C, A→B, A→J, ADI→C, ADI→G, BH→E, BI→A, BI→E, BI→G, HJ→D, HJ→G}

### 5) 无损连接性质检查 (2分)

**分解：** R₁ = {A,B,G,H,J}, R₂ = {A,D,I}, R₃ = {A,C,E,H}

**方法：Chase测试**

创建初始表格：
```
    A  B  C  D  E  G  H  I  J
R₁  a  b₁ c₁ d₁ e₁ g₁ h  i₁ j₁
R₂  a  b₂ c₂ d  e₂ g₂ h₂ i  j₂
R₃  a  b₃ c  d₃ e  g₃ h  i₃ j₃
```

应用F中的函数依赖：

1. A→BJ：由于所有行都有相同的A值(a)，使B和J值相同
2. 继续应用所有函数依赖...

应用所有函数依赖后，如果任何一行变成全部无下标变量，则分解具有无损连接。

**结果：** 该分解 **不满足** 无损连接性质。

### 6) BCNF分解 (3分)

**步骤1：** 检查R是否满足BCNF
对于F中的每个函数依赖X→Y，X必须是超键。

- BDG→AC：BDG是超键吗？(BDG)⁺ = ?
- A→BJ：A是超键吗？(A)⁺ ≠ R，所以A不是超键

R不满足BCNF。

**步骤2：** 使用违反的函数依赖A→BJ进行分解

R₁ = {A,B,J}（A→BJ的闭包）
R₂ = R - {B,J} + {A} = {A,C,D,E,G,H,I}

**步骤3：** 检查R₁和R₂是否满足BCNF

R₁ = {A,B,J} 带有函数依赖 A→BJ
- A是R₁的键，所以R₁满足BCNF

R₂ = {A,C,D,E,G,H,I}
将F投影到R₂上：{ADI→CG, BH→E, BI→AEG, HJ→DG, BDG→AC}
移除包含不在R₂中属性的函数依赖：{ADI→CG}

检查R₂是否满足BCNF...

**继续分解直到所有关系都满足BCNF。**

**最终BCNF分解：**
- R₁ = {A,B,J}
- R₂ = {A,D,I,C,G}
- R₃ = {B,H,E}
- （根据需要添加其他关系）

---

## 问题2 (8分)

### 1) 检查点和崩溃恢复 (2分)

**在t5-t6设置检查点，在t13-t14崩溃：**

**检查点时的事务状态 (t5-t6)：**
- T1：活跃（在t1开始，未提交）
- T2：未开始
- T3：活跃（在t2开始，未提交）
- T4：未开始
- T5：未开始

**崩溃时的事务状态 (t13-t14)：**
- T1：活跃（在t13提交）
- T2：活跃（在t12提交）
- T3：活跃（在t14提交）
- T4：活跃（在t8开始，在t16提交）
- T5：活跃（在t7开始，在t16提交）

**恢复操作：**
- **T1：** 在崩溃前已提交 → REDO（重做）
- **T2：** 在崩溃前已提交 → REDO（重做）
- **T3：** 崩溃时活跃 → UNDO（撤销）
- **T4：** 崩溃时活跃 → UNDO（撤销）
- **T5：** 崩溃时活跃 → UNDO（撤销）

### 2) 冲突可串行化 (2分)

**调度中的冲突：**

识别所有冲突操作（相同数据项，至少一个写操作）：

1. T1:R(X) 在 t1 vs T4:R(X) 在 t11 - 无冲突（都是读）
2. T1:R(X) 在 t1 vs T4:W(X) 在 t16 - 冲突：T1 → T4
3. T1:W(X) 在 t6 vs T4:R(X) 在 t11 - 冲突：T1 → T4
4. T1:W(X) 在 t6 vs T4:W(X) 在 t16 - 冲突：T1 → T4
5. T1:W(X) 在 t6 vs T5:R(X) 在 t7 - 冲突：T1 → T5
6. T1:W(X) 在 t6 vs T5:W(X) 在 t16 - 冲突：T1 → T5
7. T4:R(X) 在 t11 vs T5:R(X) 在 t7 - 无冲突（都是读）
8. T4:R(X) 在 t11 vs T5:W(X) 在 t16 - 冲突：T4 → T5
9. T4:W(X) 在 t16 vs T5:R(X) 在 t7 - 冲突：T5 → T4
10. T4:W(X) 在 t16 vs T5:W(X) 在 t16 - 冲突（同时 - 需要排序）

**对于Y：**
11. T1:R(Y) 在 t5 vs T2:R(Y) 在 t3 - 无冲突
12. T1:R(Y) 在 t5 vs T2:W(Y) 在 t12 - 冲突：T1 → T2
13. T1:W(Y) 在 t13 vs T2:R(Y) 在 t3 - 冲突：T2 → T1
14. T1:W(Y) 在 t13 vs T2:W(Y) 在 t12 - 冲突：T2 → T1
15. T1:W(Y) 在 t13 vs T3:R(Y) 在 t14 - 冲突：T1 → T3
16. T2:R(Y) 在 t3 vs T3:R(Y) 在 t14 - 无冲突
17. T2:W(Y) 在 t12 vs T3:R(Y) 在 t14 - 冲突：T2 → T3

**对于Z：**
18. T2:R(Z) 在 t10 vs T2:W(Z) 在 t11 - 同一事务，无冲突
19. T2:R(Z) 在 t10 vs T3:W(Z) 在 t2 - 冲突：T3 → T2
20. T2:R(Z) 在 t10 vs T4:R(Z) 在 t8 - 无冲突
21. T2:R(Z) 在 t10 vs T4:W(Z) 在 t15 - 冲突：T2 → T4
22. T2:W(Z) 在 t11 vs T3:W(Z) 在 t2 - 冲突：T3 → T2
23. T2:W(Z) 在 t11 vs T4:R(Z) 在 t8 - 冲突：T2 → T4
24. T2:W(Z) 在 t11 vs T4:W(Z) 在 t15 - 冲突：T2 → T4
25. T3:W(Z) 在 t2 vs T4:R(Z) 在 t8 - 冲突：T3 → T4
26. T3:W(Z) 在 t2 vs T4:W(Z) 在 t15 - 冲突：T3 → T4

**优先图：**
```
T3 → T2 → T1 → T4
T3 → T4
T2 → T4
T1 → T5
T5 → T4
T1 → T3
T2 → T3
```

**检查环：**
T5 → T4 和 T4 → T5 形成环！

**结果：** 该调度 **不是冲突可串行化的**，因为存在环 T4 ↔ T5。

### 3) 两阶段锁的死锁 (4分)

**死锁调度：**

```
时间  T1        T2        T3        T4        T5
t1    SL(X)
t2    R(X)      SL(Y)
t3              R(Y)      SL(Z)
t4                        W(Z)      SL(Z)
t5                                  [等待]    SL(X)
t6    SL(Y)                                   [等待]
t7    [等待]    SL(X)
t8              [等待]
```

**解释：**
- T1 持有 SL(X)，想要 SL(Y)
- T2 持有 SL(Y)，想要 SL(X)
- T3 持有 SL(Z)
- T4 等待 SL(Z)（被T3持有）
- T5 等待 SL(X)（被T1持有）

**死锁：** T1 ↔ T2（循环等待）

**等待图：**
T1 → T2 → T1（环 = 死锁）

---

## 问题3 (6分)

**查询序列：** P1, P2, P3, P2, P4, P5, P6, P5, P4, P3, P5, P4, P3, P2, P3
**缓冲池大小：** 3

### 1) LRU（最近最少使用）策略 (1.5分)

| 步骤 | 页面 | 缓冲区状态 | 命中/缺失 | 备注 |
|------|------|------------|-----------|------|
| 1 | P1 | [P1, -, -] | 缺失 | 加载P1 |
| 2 | P2 | [P1, P2, -] | 缺失 | 加载P2 |
| 3 | P3 | [P1, P2, P3] | 缺失 | 加载P3 |
| 4 | P2 | [P1, P3, P2] | 命中 | P2成为最近使用 |
| 5 | P4 | [P4, P3, P2] | 缺失 | 替换P1（LRU） |
| 6 | P5 | [P4, P5, P2] | 缺失 | 替换P3（LRU） |
| 7 | P6 | [P6, P5, P2] | 缺失 | 替换P4（LRU） |
| 8 | P5 | [P6, P2, P5] | 命中 | P5成为最近使用 |
| 9 | P4 | [P4, P2, P5] | 缺失 | 替换P6（LRU） |
| 10 | P3 | [P4, P3, P5] | 缺失 | 替换P2（LRU） |
| 11 | P5 | [P4, P3, P5] | 命中 | P5成为最近使用 |
| 12 | P4 | [P3, P5, P4] | 命中 | P4成为最近使用 |
| 13 | P3 | [P5, P4, P3] | 命中 | P3成为最近使用 |
| 14 | P2 | [P2, P4, P3] | 缺失 | 替换P5（LRU） |
| 15 | P3 | [P2, P4, P3] | 命中 | P3成为最近使用 |

**LRU结果：** 9次缺失，6次命中

### 2) MRU (Most Recently Used) Policy (1.5 marks)

| Step | Page | Buffer State | Hit/Miss | Notes |
|------|------|--------------|----------|-------|
| 1 | P1 | [P1, -, -] | Miss | Load P1 |
| 2 | P2 | [P1, P2, -] | Miss | Load P2 |
| 3 | P3 | [P1, P2, P3] | Miss | Load P3 |
| 4 | P2 | [P1, P3, P2] | Hit | P2 becomes most recent |
| 5 | P4 | [P1, P3, P4] | Miss | Replace P2 (MRU) |
| 6 | P5 | [P1, P3, P5] | Miss | Replace P4 (MRU) |
| 7 | P6 | [P1, P3, P6] | Miss | Replace P5 (MRU) |
| 8 | P5 | [P1, P3, P5] | Miss | Replace P6 (MRU) |
| 9 | P4 | [P1, P3, P4] | Miss | Replace P5 (MRU) |
| 10 | P3 | [P1, P4, P3] | Hit | P3 becomes most recent |
| 11 | P5 | [P1, P4, P5] | Miss | Replace P3 (MRU) |
| 12 | P4 | [P1, P5, P4] | Hit | P4 becomes most recent |
| 13 | P3 | [P1, P5, P3] | Miss | Replace P4 (MRU) |
| 14 | P2 | [P1, P5, P2] | Miss | Replace P3 (MRU) |
| 15 | P3 | [P1, P5, P3] | Miss | Replace P2 (MRU) |

**MRU Results:** 12 misses, 3 hits

### 3) FIFO (First In First Out) Policy (1.5 marks)

| Step | Page | Buffer State | Hit/Miss | Notes |
|------|------|--------------|----------|-------|
| 1 | P1 | [P1, -, -] | Miss | Load P1 |
| 2 | P2 | [P1, P2, -] | Miss | Load P2 |
| 3 | P3 | [P1, P2, P3] | Miss | Load P3 |
| 4 | P2 | [P1, P2, P3] | Hit | No replacement |
| 5 | P4 | [P4, P2, P3] | Miss | Replace P1 (first in) |
| 6 | P5 | [P4, P5, P3] | Miss | Replace P2 (first in) |
| 7 | P6 | [P4, P5, P6] | Miss | Replace P3 (first in) |
| 8 | P5 | [P4, P5, P6] | Hit | No replacement |
| 9 | P4 | [P4, P5, P6] | Hit | No replacement |
| 10 | P3 | [P3, P5, P6] | Miss | Replace P4 (first in) |
| 11 | P5 | [P3, P5, P6] | Hit | No replacement |
| 12 | P4 | [P3, P4, P6] | Miss | Replace P5 (first in) |
| 13 | P3 | [P3, P4, P6] | Hit | No replacement |
| 14 | P2 | [P3, P4, P2] | Miss | Replace P6 (first in) |
| 15 | P3 | [P3, P4, P2] | Hit | No replacement |

**FIFO Results:** 9 misses, 6 hits

### 4) Performance comparison (1.5 marks)

**Results summary:**
- **LRU:** 9 misses, 6 hits (60% hit rate)
- **MRU:** 12 misses, 3 hits (20% hit rate)  
- **FIFO:** 9 misses, 6 hits (60% hit rate)

**Best performing policy:** LRU and FIFO tie for best performance.

**Explanation:**
- **LRU performs well** because the query has temporal locality - recently accessed pages (like P3, P4, P5) are accessed again soon.
- **MRU performs poorly** because it replaces the most recently used page, which is often needed again soon in this query pattern.
- **FIFO performs well** by coincidence in this particular sequence, maintaining a good mix of pages.

**Winner:** **LRU** is generally the best choice for this type of query pattern due to its exploitation of temporal locality.

---

**Total: 26 marks**
