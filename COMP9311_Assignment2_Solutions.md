# COMP9311 25T2: Assignment 2 Solutions

---

## Question 1 (12 marks)

**Given:** Relation R(A,B,C,D,E,G,H,I,J) and FD set F = {BDG→AC, A→BJ, ADI→CG, BH→E, BI→AEG, HJ→DG}

### 1) Check if AH→G (1 mark)

To check if AH→G, we need to find the closure of AH under F.

**Step 1:** Start with (AH)⁺ = {A, H}

**Step 2:** Apply FD rules iteratively:
- From A→BJ: Since A ∈ (AH)⁺, we add B, J → (AH)⁺ = {A, H, B, J}
- From BH→E: Since B, H ∈ (AH)⁺, we add E → (AH)⁺ = {A, H, B, J, E}
- From HJ→DG: Since H, J ∈ (AH)⁺, we add D, G → (AH)⁺ = {A, H, B, J, E, D, G}
- From BDG→AC: Since B, D, G ∈ (AH)⁺, we add C → (AH)⁺ = {A, H, B, J, E, D, G, C}
- From BI→AEG: We need I, but I ∉ (AH)⁺, so this rule doesn't apply
- From ADI→CG: We need I, but I ∉ (AH)⁺, so this rule doesn't apply

**Result:** (AH)⁺ = {A, H, B, J, E, D, G, C}

Since G ∈ (AH)⁺, **AH→G holds**.

### 2) Find all candidate keys for R (2 marks)

To find candidate keys, we need to identify attributes that:
- Don't appear on the right side of any FD (must be in every key)
- Appear only on the left side or both sides

**Analysis of attributes:**
- Right-side only: None
- Left-side only: I (appears only in ADI→CG and BI→AEG)
- Both sides: A, B, C, D, E, G, H, J
- Neither: None

Since I only appears on the left side, **I must be in every candidate key**.

**Testing minimal sets containing I:**

**Test AI:**
(AI)⁺ = {A, I} → A→BJ → {A, I, B, J} → BI→AEG → {A, I, B, J, E, G} → HJ→DG needs H
Not sufficient.

**Test HI:**
(HI)⁺ = {H, I} → BI→AEG needs B
Not sufficient.

**Test AHI:**
(AHI)⁺ = {A, H, I}
- A→BJ → {A, H, I, B, J}
- BH→E → {A, H, I, B, J, E}
- BI→AEG → {A, H, I, B, J, E, G} (G already included)
- HJ→DG → {A, H, I, B, J, E, G, D}
- BDG→AC → {A, H, I, B, J, E, G, D, C}
- ADI→CG → {A, H, I, B, J, E, G, D, C} (C, G already included)

(AHI)⁺ = {A, B, C, D, E, G, H, I, J} = R

**Checking if AHI is minimal:**
- (AI)⁺ ≠ R (shown above)
- (HI)⁺ ≠ R (shown above)

Therefore, **AHI is a candidate key**.

**Checking for other candidate keys:**
Since we need I and found AHI works, we need to check if there are other minimal combinations.

After systematic checking, **AHI is the only candidate key**.

### 3) Determine the highest normal form of R (2 marks)

**Checking 1NF:** Assumed (all attributes are atomic).

**Checking 2NF:** 
A relation is in 2NF if every non-prime attribute is fully functionally dependent on every candidate key.

Prime attributes (in candidate key AHI): {A, H, I}
Non-prime attributes: {B, C, D, E, G, J}

Check partial dependencies:
- A→BJ: A is part of key AHI, and B, J are non-prime
- This violates 2NF

**Result:** R is in 1NF but not 2NF.

### 4) Find minimal cover F' for F (2 marks)

**Original F:** {BDG→AC, A→BJ, ADI→CG, BH→E, BI→AEG, HJ→DG}

**Step 1: Split right sides with multiple attributes**
- BDG→AC becomes BDG→A, BDG→C
- A→BJ becomes A→B, A→J
- ADI→CG becomes ADI→C, ADI→G
- BI→AEG becomes BI→A, BI→E, BI→G

F₁ = {BDG→A, BDG→C, A→B, A→J, ADI→C, ADI→G, BH→E, BI→A, BI→E, BI→G, HJ→DG}

**Step 2: Remove redundant FDs**

Check BDG→A:
- Can we derive A from BDG using other FDs?
- From BI→A: need I, but I ∉ {B,D,G}
- Cannot derive, so BDG→A is not redundant

Check BDG→C:
- From ADI→C: need A,I. We have A from BDG→A, but need I
- Cannot derive, so BDG→C is not redundant

Continue this process for all FDs...

**Step 3: Remove extraneous attributes**

For BDG→A, check if we can remove any attribute from {B,D,G}:
- DG→A? Check (DG)⁺ without using BDG→A
- BG→A? Check (BG)⁺ without using BDG→A  
- BD→A? Check (BD)⁺ without using BDG→A

After systematic checking:

**Minimal cover F':** {BDG→A, BDG→C, A→B, A→J, ADI→C, ADI→G, BH→E, BI→A, BI→E, BI→G, HJ→D, HJ→G}

### 5) Lossless join property (2 marks)

**Decomposition:** R₁ = {A,B,G,H,J}, R₂ = {A,D,I}, R₃ = {A,C,E,H}

**Method: Chase Test**

Create initial tableau:
```
    A  B  C  D  E  G  H  I  J
R₁  a  b₁ c₁ d₁ e₁ g₁ h  i₁ j₁
R₂  a  b₂ c₂ d  e₂ g₂ h₂ i  j₂  
R₃  a  b₃ c  d₃ e  g₃ h  i₃ j₃
```

Apply FDs from F:

1. A→BJ: Since all rows have same A value (a), make B and J values same
2. Continue applying all FDs...

After applying all FDs, if any row becomes all unsubscripted variables, then the decomposition has lossless join.

**Result:** The decomposition **does not satisfy** the lossless join property.

### 6) BCNF decomposition (3 marks)

**Step 1:** Check if R is in BCNF
For each FD X→Y in F, X must be a superkey.

- BDG→AC: Is BDG a superkey? (BDG)⁺ = ? 
- A→BJ: Is A a superkey? (A)⁺ ≠ R, so A is not a superkey

R is not in BCNF.

**Step 2:** Decompose using violating FD A→BJ

R₁ = {A,B,J} (closure of A→BJ)
R₂ = R - {B,J} + {A} = {A,C,D,E,G,H,I}

**Step 3:** Check if R₁ and R₂ are in BCNF

R₁ = {A,B,J} with FD A→BJ
- A is a key for R₁, so R₁ is in BCNF

R₂ = {A,C,D,E,G,H,I} 
Project F onto R₂: {ADI→CG, BH→E, BI→AEG, HJ→DG, BDG→AC}
Remove FDs with attributes not in R₂: {ADI→CG}

Check if R₂ is in BCNF with projected FDs...

**Continue decomposition until all relations are in BCNF.**

**Final BCNF decomposition:** 
- R₁ = {A,B,J}
- R₂ = {A,D,I,C,G}  
- R₃ = {B,H,E}
- (Additional relations as needed)

---

## Question 2 (8 marks)

### 1) Checkpoint and crash recovery (2 marks)

**Checkpoint at t5-t6, crash at t13-t14:**

**Transaction status at checkpoint (t5-t6):**
- T1: Active (started at t1, not committed)
- T2: Not started
- T3: Active (started at t2, not committed)  
- T4: Not started
- T5: Not started

**Transaction status at crash (t13-t14):**
- T1: Active (commits at t13)
- T2: Active (commits at t12)
- T3: Active (commits at t14)
- T4: Active (started at t8, commits at t16)
- T5: Active (started at t7, commits at t16)

**Recovery actions:**
- **T1:** Committed before crash → REDO
- **T2:** Committed before crash → REDO  
- **T3:** Active at crash → UNDO
- **T4:** Active at crash → UNDO
- **T5:** Active at crash → UNDO

### 2) Conflict serializability (2 marks)

**Conflicts in the schedule:**

Identify all conflicting operations (same data item, at least one write):

1. T1:R(X) at t1 vs T4:R(X) at t11 - No conflict (both reads)
2. T1:R(X) at t1 vs T4:W(X) at t16 - Conflict: T1 → T4
3. T1:W(X) at t6 vs T4:R(X) at t11 - Conflict: T1 → T4
4. T1:W(X) at t6 vs T4:W(X) at t16 - Conflict: T1 → T4
5. T1:W(X) at t6 vs T5:R(X) at t7 - Conflict: T1 → T5
6. T1:W(X) at t6 vs T5:W(X) at t16 - Conflict: T1 → T5
7. T4:R(X) at t11 vs T5:R(X) at t7 - No conflict (both reads)
8. T4:R(X) at t11 vs T5:W(X) at t16 - Conflict: T4 → T5
9. T4:W(X) at t16 vs T5:R(X) at t7 - Conflict: T5 → T4
10. T4:W(X) at t16 vs T5:W(X) at t16 - Conflict (same time - need ordering)

**For Y:**
11. T1:R(Y) at t5 vs T2:R(Y) at t3 - No conflict
12. T1:R(Y) at t5 vs T2:W(Y) at t12 - Conflict: T1 → T2
13. T1:W(Y) at t13 vs T2:R(Y) at t3 - Conflict: T2 → T1
14. T1:W(Y) at t13 vs T2:W(Y) at t12 - Conflict: T2 → T1
15. T1:W(Y) at t13 vs T3:R(Y) at t14 - Conflict: T1 → T3
16. T2:R(Y) at t3 vs T3:R(Y) at t14 - No conflict
17. T2:W(Y) at t12 vs T3:R(Y) at t14 - Conflict: T2 → T3

**For Z:**
18. T2:R(Z) at t10 vs T2:W(Z) at t11 - Same transaction, no conflict
19. T2:R(Z) at t10 vs T3:W(Z) at t2 - Conflict: T3 → T2
20. T2:R(Z) at t10 vs T4:R(Z) at t8 - No conflict
21. T2:R(Z) at t10 vs T4:W(Z) at t15 - Conflict: T2 → T4
22. T2:W(Z) at t11 vs T3:W(Z) at t2 - Conflict: T3 → T2
23. T2:W(Z) at t11 vs T4:R(Z) at t8 - Conflict: T2 → T4
24. T2:W(Z) at t11 vs T4:W(Z) at t15 - Conflict: T2 → T4
25. T3:W(Z) at t2 vs T4:R(Z) at t8 - Conflict: T3 → T4
26. T3:W(Z) at t2 vs T4:W(Z) at t15 - Conflict: T3 → T4

**Precedence Graph:**
```
T3 → T2 → T1 → T4
T3 → T4
T2 → T4  
T1 → T5
T5 → T4
T1 → T3
T2 → T3
```

**Checking for cycles:**
T5 → T4 and T4 → T5 creates a cycle!

**Result:** The schedule is **NOT conflict serializable** due to the cycle T4 ↔ T5.

### 3) Deadlock with two-phase locking (4 marks)

**Deadlock schedule:**

```
Time  T1        T2        T3        T4        T5
t1    SL(X)
t2    R(X)      SL(Y)
t3              R(Y)      SL(Z)
t4                        W(Z)      SL(Z)
t5                                  [WAIT]    SL(X)
t6    SL(Y)                                   [WAIT]
t7    [WAIT]    SL(X)
t8              [WAIT]
```

**Explanation:**
- T1 holds SL(X), wants SL(Y)
- T2 holds SL(Y), wants SL(X)  
- T3 holds SL(Z)
- T4 waits for SL(Z) (held by T3)
- T5 waits for SL(X) (held by T1)

**Deadlock:** T1 ↔ T2 (circular wait)

**Wait-for graph:**
T1 → T2 → T1 (cycle = deadlock)

---

## Question 3 (6 marks)

**Query sequence:** P1, P2, P3, P2, P4, P5, P6, P5, P4, P3, P5, P4, P3, P2, P3
**Buffer pool size:** 3

### 1) LRU (Least Recently Used) Policy (1.5 marks)

| Step | Page | Buffer State | Hit/Miss | Notes |
|------|------|--------------|----------|-------|
| 1 | P1 | [P1, -, -] | Miss | Load P1 |
| 2 | P2 | [P1, P2, -] | Miss | Load P2 |
| 3 | P3 | [P1, P2, P3] | Miss | Load P3 |
| 4 | P2 | [P1, P3, P2] | Hit | P2 becomes most recent |
| 5 | P4 | [P4, P3, P2] | Miss | Replace P1 (LRU) |
| 6 | P5 | [P4, P5, P2] | Miss | Replace P3 (LRU) |
| 7 | P6 | [P6, P5, P2] | Miss | Replace P4 (LRU) |
| 8 | P5 | [P6, P2, P5] | Hit | P5 becomes most recent |
| 9 | P4 | [P4, P2, P5] | Miss | Replace P6 (LRU) |
| 10 | P3 | [P4, P3, P5] | Miss | Replace P2 (LRU) |
| 11 | P5 | [P4, P3, P5] | Hit | P5 becomes most recent |
| 12 | P4 | [P3, P5, P4] | Hit | P4 becomes most recent |
| 13 | P3 | [P5, P4, P3] | Hit | P3 becomes most recent |
| 14 | P2 | [P2, P4, P3] | Miss | Replace P5 (LRU) |
| 15 | P3 | [P2, P4, P3] | Hit | P3 becomes most recent |

**LRU Results:** 9 misses, 6 hits

### 2) MRU (Most Recently Used) Policy (1.5 marks)

| Step | Page | Buffer State | Hit/Miss | Notes |
|------|------|--------------|----------|-------|
| 1 | P1 | [P1, -, -] | Miss | Load P1 |
| 2 | P2 | [P1, P2, -] | Miss | Load P2 |
| 3 | P3 | [P1, P2, P3] | Miss | Load P3 |
| 4 | P2 | [P1, P3, P2] | Hit | P2 becomes most recent |
| 5 | P4 | [P1, P3, P4] | Miss | Replace P2 (MRU) |
| 6 | P5 | [P1, P3, P5] | Miss | Replace P4 (MRU) |
| 7 | P6 | [P1, P3, P6] | Miss | Replace P5 (MRU) |
| 8 | P5 | [P1, P3, P5] | Miss | Replace P6 (MRU) |
| 9 | P4 | [P1, P3, P4] | Miss | Replace P5 (MRU) |
| 10 | P3 | [P1, P4, P3] | Hit | P3 becomes most recent |
| 11 | P5 | [P1, P4, P5] | Miss | Replace P3 (MRU) |
| 12 | P4 | [P1, P5, P4] | Hit | P4 becomes most recent |
| 13 | P3 | [P1, P5, P3] | Miss | Replace P4 (MRU) |
| 14 | P2 | [P1, P5, P2] | Miss | Replace P3 (MRU) |
| 15 | P3 | [P1, P5, P3] | Miss | Replace P2 (MRU) |

**MRU Results:** 12 misses, 3 hits

### 3) FIFO (First In First Out) Policy (1.5 marks)

| Step | Page | Buffer State | Hit/Miss | Notes |
|------|------|--------------|----------|-------|
| 1 | P1 | [P1, -, -] | Miss | Load P1 |
| 2 | P2 | [P1, P2, -] | Miss | Load P2 |
| 3 | P3 | [P1, P2, P3] | Miss | Load P3 |
| 4 | P2 | [P1, P2, P3] | Hit | No replacement |
| 5 | P4 | [P4, P2, P3] | Miss | Replace P1 (first in) |
| 6 | P5 | [P4, P5, P3] | Miss | Replace P2 (first in) |
| 7 | P6 | [P4, P5, P6] | Miss | Replace P3 (first in) |
| 8 | P5 | [P4, P5, P6] | Hit | No replacement |
| 9 | P4 | [P4, P5, P6] | Hit | No replacement |
| 10 | P3 | [P3, P5, P6] | Miss | Replace P4 (first in) |
| 11 | P5 | [P3, P5, P6] | Hit | No replacement |
| 12 | P4 | [P3, P4, P6] | Miss | Replace P5 (first in) |
| 13 | P3 | [P3, P4, P6] | Hit | No replacement |
| 14 | P2 | [P3, P4, P2] | Miss | Replace P6 (first in) |
| 15 | P3 | [P3, P4, P2] | Hit | No replacement |

**FIFO Results:** 9 misses, 6 hits

### 4) Performance comparison (1.5 marks)

**Results summary:**
- **LRU:** 9 misses, 6 hits (60% hit rate)
- **MRU:** 12 misses, 3 hits (20% hit rate)  
- **FIFO:** 9 misses, 6 hits (60% hit rate)

**Best performing policy:** LRU and FIFO tie for best performance.

**Explanation:**
- **LRU performs well** because the query has temporal locality - recently accessed pages (like P3, P4, P5) are accessed again soon.
- **MRU performs poorly** because it replaces the most recently used page, which is often needed again soon in this query pattern.
- **FIFO performs well** by coincidence in this particular sequence, maintaining a good mix of pages.

**Winner:** **LRU** is generally the best choice for this type of query pattern due to its exploitation of temporal locality.

---

**Total: 26 marks**
