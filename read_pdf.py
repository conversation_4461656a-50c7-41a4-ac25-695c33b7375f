import PyPDF2
import sys

def read_pdf(file_path):
    try:
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            print(f"PDF文件: {file_path}")
            print(f"总页数: {len(pdf_reader.pages)}")
            print("=" * 50)
            
            # 读取所有页面的内容
            full_text = ""
            for page_num, page in enumerate(pdf_reader.pages):
                text = page.extract_text()
                print(f"\n--- 第 {page_num + 1} 页 ---")
                print(text)
                full_text += f"\n--- 第 {page_num + 1} 页 ---\n" + text
                
            return full_text
            
    except Exception as e:
        print(f"读取PDF时出错: {e}")
        return None

if __name__ == "__main__":
    pdf_file = "COMP9311_25T2_Assignment_2.pdf"
    content = read_pdf(pdf_file)
